<?php

namespace Webkul\Theme\Repositories;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Webkul\Core\Eloquent\Repository;
use Webkul\Theme\Contracts\ThemeCustomization;

class ThemeCustomizationRepository extends Repository
{
    /**
     * Specify model class name.
     */
    public function model(): string
    {
        return ThemeCustomization::class;
    }

    /**
     * Update the specified theme
     *
     * @param  array  $data
     * @param  int  $id
     */
    public function update($data, $id): ThemeCustomization
    {
        $locale = core()->getRequestedLocaleCode();

        if ($data['type'] == 'static_content') {
            $data[$locale]['options']['html'] = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $data[$locale]['options']['html']);
            $data[$locale]['options']['css'] = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $data[$locale]['options']['css']);
        }

        if (in_array($data['type'], ['image_carousel', 'services_content'])) {
            unset($data[$locale]['options']);
        }

        $theme = parent::update($data, $id);

        if (in_array($data['type'], ['image_carousel', 'services_content'])) {
            $this->uploadImage(request()->all(), $theme);
        }

        if ($data['type'] === 'brand_content') {
            $this->uploadBrandImages(request()->all(), $theme);
        }

        return $theme;
    }

    /**
     * Mass update the status of themes in the repository.
     *
     * This method updates multiple records in the database based on the provided
     * theme IDs.
     *
     * @param  int  $themeIds
     * @return int The number of records updated.
     */
    public function massUpdateStatus(array $data, array $themeIds)
    {
        return $this->model->whereIn('id', $themeIds)->update($data);
    }

    /**
     * Upload images
     *
     * @return void|string
     */
    public function uploadImage(array $data, ThemeCustomization $theme)
    {
        $locale = core()->getRequestedLocaleCode();
        if($locale == 'zh_CN') {
            $locale = 'en';
        }
        if (isset($data[$locale]['deleted_sliders'])) {
            foreach ($data[$locale]['deleted_sliders'] as $slider) {
                Storage::delete(str_replace('storage/', '', $slider['image']));
            }
        }

        if (! isset($data[$locale]['options'])) {
            return;
        }

        $options = [];

        foreach ($data[$locale]['options'] as $image) {
            if (isset($image['service_icon'])) {
                $options['services'][] = [
                    'service_icon' => $image['service_icon'],
                    'description'  => $image['description'],
                    'title'        => $image['title'],
                ];
            } elseif ($image['image'] instanceof UploadedFile) {
                try {
                    $manager = new ImageManager;

                    $path = 'theme/'.$theme->id.'/'.Str::random(40).'.webp';

                    Storage::put($path, $manager->make($image['image'])->encode('webp'));
                } catch (\Exception $e) {
                    session()->flash('error', $e->getMessage());

                    return redirect()->back();
                }

                if (($data['type'] ?? '') == 'static_content') {
                    return Storage::url($path);
                }

                $options['images'][] = [
                    'image' => 'storage/'.$path,
                    'link'  => $image['link'],
                    'title' => $image['title'],
                    'intro' => $image['intro'] ?? '',
                ];
            } else {
                $options['images'][] = $image;
            }
        }

        $translatedModel = $theme->translate($locale);
        $translatedModel->options = $options ?? [];
        $translatedModel->theme_customization_id = $theme->id;
        $translatedModel->save();
    }

    /**
     * Upload brand images
     *
     * @return void
     */
    public function uploadBrandImages(array $data, ThemeCustomization $theme)
    {
        $locale = core()->getRequestedLocaleCode();
        if($locale == 'zh_CN') {
            $locale = 'en';
        }

        // 处理删除的品牌图片
        if (isset($data[$locale]['deleted_brands'])) {
            foreach ($data[$locale]['deleted_brands'] as $brand) {
                if (isset($brand['image']) && !str_starts_with($brand['image'], 'blob:')) {
                    Storage::delete(str_replace('storage/', '', $brand['image']));
                }
            }
        }

        $options = [];
        $brands = [];

        // 处理标题数据
        if (isset($data[$locale]['options']['title'])) {
            $options['title'] = $data[$locale]['options']['title'];
        }

        // 处理现有品牌数据
        if (isset($data[$locale]['options']['brands'])) {
            foreach ($data[$locale]['options']['brands'] as $brand) {
                // 过滤掉blob URL，这些是临时的浏览器URL
                if (isset($brand['image']) && str_starts_with($brand['image'], 'blob:')) {
                    continue; // 跳过blob URL，不保存到数据库
                }

                $brands[] = [
                    'name' => $brand['name'] ?? '',
                    'url' => $brand['url'] ?? '',
                    'image' => $brand['image'] ?? '',
                ];
            }
        }

        // 处理新添加的品牌（从表单字段获取）
        if (isset($data[$locale]['name']) && isset($data['brand_image'])) {
            $newBrand = [
                'name' => $data[$locale]['name'],
                'url' => $data[$locale]['url'] ?? '',
                'image' => '',
            ];

            // 处理图片上传
            if ($data['brand_image'] instanceof UploadedFile) {
                try {
                    $manager = new ImageManager;
                    $path = 'theme/'.$theme->id.'/brands/'.Str::random(40).'.webp';
                    Storage::put($path, $manager->make($data['brand_image'])->encode('webp'));
                    $newBrand['image'] = 'storage/'.$path;
                } catch (\Exception $e) {
                    session()->flash('error', $e->getMessage());
                    return redirect()->back();
                }
            }

            $brands[] = $newBrand;
        }

        $options['brands'] = $brands;

        $translatedModel = $theme->translate($locale);
        $translatedModel->options = $options;
        $translatedModel->theme_customization_id = $theme->id;
        $translatedModel->save();

        // 同步到其他语种
        $this->autoFillTranslations($theme, $locale, ['options' => $options]);
    }

    /**
     * 自动补全其他语种的翻译数据
     * 只有当某个语种不存在对应的字段内容时才补全
     *
     * @param ThemeCustomization $theme
     * @param string $currentLocale
     * @param array $currentData
     * @return void
     */
    public function autoFillTranslations(ThemeCustomization $theme, string $currentLocale, array $currentData)
    {
        try {
            // 获取当前渠道的所有语种
            $allChannels = core()->getAllChannels();
            $channel = $allChannels->find($theme->channel_id);
            
            if (!$channel) {
                return;
            }

            $locales = $channel->locales;
           
            // 获取当前语种的翻译数据
            $currentTranslation = $theme->translations->where('locale', $currentLocale)->first();
            $sourceOptions = $currentTranslation ? $currentTranslation->options : ($currentData['options'] ?? []);
            
            // 如果源数据为空，则不需要补全
            if (empty($sourceOptions)) {
                return;
            }

            // 遍历所有语种，检查并补全缺失的翻译
            foreach ($locales as $locale) {
                // 跳过当前语种
                if ($locale->code === $currentLocale) {
                    continue;
                }

                // 检查该语种是否已存在翻译
                $existingTranslation = $theme->translations->where('locale', $locale->code)->first();
               
                // 对于品牌内容，总是同步数据以确保一致性
                if ($theme->type === 'brand_content') {
                    if (!$existingTranslation) {
                        // 创建新的翻译记录
                        $theme->translations()->create([
                            'theme_customization_id' => $theme->id,
                            'locale' => $locale->code,
                            'options' => $sourceOptions,
                        ]);
                    } else {
                        // 更新现有翻译记录，确保品牌数据同步
                        $existingTranslation->options = $sourceOptions;
                        $existingTranslation->save();
                    }
                } else {
                    // 对于其他类型，只有当options为空时才补全
                    if (!$existingTranslation) {
                        // 创建新的翻译记录
                        $theme->translations()->create([
                            'theme_customization_id' => $theme->id,
                            'locale' => $locale->code,
                            'options' => $sourceOptions,
                        ]);
                    } elseif (empty($existingTranslation->options)) {
                        // 更新现有翻译记录（如果options为空）
                        $existingTranslation->options = $sourceOptions;
                        $existingTranslation->save();
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            Log::warning('主题多语言自动补全失败: ' . $e->getMessage(), [
                'theme_id' => $theme->id,
                'current_locale' => $currentLocale,
            ]);
        }
    }
}
